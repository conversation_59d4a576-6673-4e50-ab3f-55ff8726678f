/**
 * 网络系统
 * 负责管理网络连接、数据同步和多用户支持
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Engine } from '../core/Engine';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkManager } from './NetworkManager';
import { NetworkConnection } from './NetworkConnection';
import { NetworkEvent, NetworkEventType } from './NetworkEvent';
import { NetworkEntityComponent  } from './components/NetworkEntityComponent';
import { NetworkTransformComponent  } from './components/NetworkTransformComponent';
import { NetworkUserComponent } from './components/NetworkUserComponent';
import { Debug } from '../utils/Debug';
import { WebRTCConnectionManager } from './WebRTCConnectionManager';
import { MediaStreamManager, MediaStreamType } from './MediaStreamManager';
import { EntitySyncManager } from './EntitySyncManager';
import { UserSessionManager, UserRole, UserPermission } from './UserSessionManager';
import { NetworkEventDispatcher } from './NetworkEventDispatcher';
import { NetworkEventBuffer, EventPriority } from './NetworkEventBuffer';
import { BandwidthController, BandwidthControlStrategy } from './BandwidthController';
import { NetworkQualityMonitor } from './NetworkQualityMonitor';
import { DataCompressor, CompressionAlgorithm, CompressionLevel } from './DataCompressor';
import { ServiceDiscoveryClient, ServiceInstance } from './ServiceDiscoveryClient';
import { MicroserviceClient } from './MicroserviceClient';

/**
 * 网络系统配置选项
 */
export interface NetworkSystemOptions {
  /** 是否自动连接到服务器 */
  autoConnect?: boolean;
  /** 服务器URL */
  serverUrl?: string;
  /** 房间ID */
  roomId?: string;
  /** 用户ID */
  userId?: string;
  /** 用户名 */
  username?: string;
  /** 是否启用WebRTC */
  enableWebRTC?: boolean;
  /** ICE服务器配置 */
  iceServers?: RTCIceServer[];
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 数据同步间隔（毫秒） */
  syncInterval?: number;
  /** 是否启用数据压缩 */
  enableCompression?: boolean;
  /** 是否启用媒体流 */
  enableMediaStream?: boolean;
  /** 是否启用音频 */
  enableAudio?: boolean;
  /** 是否启用视频 */
  enableVideo?: boolean;
  /** 是否启用屏幕共享 */
  enableScreenShare?: boolean;
  /** 是否启用网络质量监控 */
  enableNetworkQualityMonitor?: boolean;
  /** 是否启用带宽控制 */
  enableBandwidthControl?: boolean;
  /** 带宽控制策略 */
  bandwidthControlStrategy?: BandwidthControlStrategy;
  /** 最大上行带宽（字节/秒） */
  maxUploadBandwidth?: number;
  /** 最大下行带宽（字节/秒） */
  maxDownloadBandwidth?: number;
  /** 压缩算法 */
  compressionAlgorithm?: CompressionAlgorithm;
  /** 压缩级别 */
  compressionLevel?: CompressionLevel;
  /** 是否启用实体同步 */
  enableEntitySync?: boolean;
  /** 是否启用用户会话管理 */
  enableUserSessionManagement?: boolean;
  /** 默认用户角色 */
  defaultUserRole?: UserRole;
  /** 是否启用权限检查 */
  enablePermissionCheck?: boolean;
  /** 是否启用事件缓冲 */
  enableEventBuffer?: boolean;
  /** 是否启用事件日志 */
  enableEventLogging?: boolean;
  /** 是否启用服务发现 */
  enableServiceDiscovery?: boolean;
  /** 服务注册中心URL */
  serviceRegistryUrl?: string;
  /** 是否启用微服务通信 */
  enableMicroserviceClient?: boolean;
  /** API网关URL */
  apiGatewayUrl?: string;
  /** 是否使用API网关 */
  useApiGateway?: boolean;
}

/**
 * 网络系统状态
 */
export enum NetworkState {
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 连接错误 */
  ERROR = 'error',
}

/**
 * 网络系统
 * 负责管理网络连接、数据同步和多用户支持
 */
export class NetworkSystem extends System {
  /** 网络管理器 */
  private networkManager: NetworkManager;

  /** 网络连接状态 */
  private connectionState: NetworkState = NetworkState.DISCONNECTED;

  /** 配置选项 */
  private networkOptions: NetworkSystemOptions;

  /** 重连尝试次数 */
  private reconnectAttempts: number = 0;

  /** 重连定时器ID */
  private reconnectTimerId: number | null = null;

  /** 同步定时器ID */
  private syncTimerId: number | null = null;

  /** 网络实体映射表 */
  private networkEntities: Map<string, Entity> = new Map();

  /** 本地用户ID */
  private localUserId: string | null = null;

  /** 远程用户映射表 */
  private remoteUsers: Map<string, Entity> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** WebRTC连接管理器 */
  private webRTCConnectionManager: WebRTCConnectionManager | null = null;

  /** 媒体流管理器 */
  private mediaStreamManager: MediaStreamManager | null = null;

  /** 实体同步管理器 */
  private entitySyncManager: EntitySyncManager | null = null;

  /** 用户会话管理器 */
  private userSessionManager: UserSessionManager | null = null;

  /** 网络事件分发器 */
  private networkEventDispatcher: NetworkEventDispatcher | null = null;

  /** 网络事件缓冲器 */
  private networkEventBuffer: NetworkEventBuffer | null = null;

  /** 带宽控制器 */
  private bandwidthController: BandwidthController | null = null;

  /** 网络质量监控器 */
  private networkQualityMonitor: NetworkQualityMonitor | null = null;

  /** 数据压缩器 */
  private dataCompressor: DataCompressor | null = null;

  /** 服务发现客户端 */
  private serviceDiscoveryClient: ServiceDiscoveryClient | null = null;

  /** 微服务客户端 */
  private microserviceClient: MicroserviceClient | null = null;

  /** 引擎实例 */
  private engine: Engine | null = null;

  /** 世界实例 */
  private world: any = null;

  /**
   * 创建网络系统
   * @param options 配置选项
   */
  constructor(options: NetworkSystemOptions = {}) {
    super(0);

    this.networkOptions = {
      autoConnect: false,
      serverUrl: 'wss://localhost:8080',
      enableWebRTC: true,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      syncInterval: 100,
      enableCompression: true,
      enableMediaStream: true,
      enableAudio: false,
      enableVideo: false,
      enableScreenShare: false,
      enableNetworkQualityMonitor: true,
      enableBandwidthControl: true,
      bandwidthControlStrategy: BandwidthControlStrategy.ADAPTIVE,
      maxUploadBandwidth: 1024 * 1024, // 1MB/s
      maxDownloadBandwidth: 1024 * 1024, // 1MB/s
      compressionAlgorithm: CompressionAlgorithm.LZ_STRING,
      compressionLevel: CompressionLevel.MEDIUM,
      enableEntitySync: true,
      enableUserSessionManagement: true,
      defaultUserRole: UserRole.USER,
      enablePermissionCheck: true,
      enableEventBuffer: true,
      enableEventLogging: false,
      enableServiceDiscovery: true,
      serviceRegistryUrl: 'http://localhost:4010/api/registry',
      enableMicroserviceClient: true,
      apiGatewayUrl: 'http://localhost:3000/api',
      useApiGateway: true,
      ...options,
    };

    // 创建数据压缩器
    if (this.networkOptions.enableCompression) {
      this.dataCompressor = new DataCompressor({
        algorithm: this.networkOptions.compressionAlgorithm,
        level: this.networkOptions.compressionLevel,
        adaptive: true,
      });
    }

    // 创建网络质量监控器
    if (this.networkOptions.enableNetworkQualityMonitor) {
      this.networkQualityMonitor = new NetworkQualityMonitor();
    }

    // 创建带宽控制器
    if (this.networkOptions.enableBandwidthControl) {
      this.bandwidthController = new BandwidthController({
        maxUploadBandwidth: this.networkOptions.maxUploadBandwidth,
        maxDownloadBandwidth: this.networkOptions.maxDownloadBandwidth,
        strategy: this.networkOptions.bandwidthControlStrategy,
      });
    }

    // 创建服务发现客户端
    if (this.networkOptions.enableServiceDiscovery) {
      this.serviceDiscoveryClient = new ServiceDiscoveryClient({
        registryUrl: this.networkOptions.serviceRegistryUrl,
      });
    }

    // 创建微服务客户端
    if (this.networkOptions.enableMicroserviceClient) {
      this.microserviceClient = new MicroserviceClient({
        serviceDiscoveryClient: this.serviceDiscoveryClient,
        apiGatewayUrl: this.networkOptions.apiGatewayUrl,
        useApiGateway: this.networkOptions.useApiGateway,
        useServiceDiscovery: this.networkOptions.enableServiceDiscovery,
      });
    }

    // 创建网络事件缓冲器
    if (this.networkOptions.enableEventBuffer) {
      this.networkEventBuffer = new NetworkEventBuffer({
        maxBufferSize: 1000,
        processInterval: 50,
        autoProcess: true,
        maxEventsPerProcess: 10,
      });
    }

    // 创建网络事件分发器
    this.networkEventDispatcher = new NetworkEventDispatcher({
      useEventBuffer: this.networkOptions.enableEventBuffer,
      enableEventLogging: this.networkOptions.enableEventLogging,
      eventBufferConfig: {
        maxBufferSize: 1000,
        processInterval: 50,
        autoProcess: true,
        maxEventsPerProcess: 10,
      },
    });

    // 创建网络管理器
    this.networkManager = new NetworkManager(this.networkOptions);

    // 设置网络事件监听器
    this.setupEventListeners();

    // 如果设置了自动连接，则连接到服务器
    if (this.networkOptions.autoConnect && this.networkOptions.serverUrl) {
      this.connect(this.networkOptions.serverUrl, this.networkOptions.roomId);
    }
  }

  /**
   * 设置引擎实例
   * @param engine 引擎实例
   */
  public setEngine(engine: Engine): void {
    this.engine = engine;
    this.world = engine.getWorld();
  }

  /**
   * 设置世界实例
   * @param world 世界实例
   */
  public setWorld(world: any): void {
    this.world = world;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    Debug.log('NetworkSystem', 'Initializing network system');

    // 网络组件会在使用时自动注册，无需手动注册

    // 初始化本地用户ID
    if (this.networkOptions.userId) {
      this.localUserId = this.networkOptions.userId;
    } else {
      // 生成随机用户ID
      this.localUserId = `user_${Math.random().toString(36).substring(2, 9)}`;
    }

    // 初始化服务发现客户端
    if (this.serviceDiscoveryClient) {
      this.serviceDiscoveryClient.initialize();
    }

    // 初始化微服务客户端
    if (this.microserviceClient) {
      this.microserviceClient.initialize();
    }

    // 初始化用户会话管理器
    if (this.networkOptions.enableUserSessionManagement) {
      this.userSessionManager = new UserSessionManager({
        defaultRole: this.networkOptions.defaultUserRole,
        enablePermissionCheck: this.networkOptions.enablePermissionCheck,
      });

      // 创建本地用户会话
      if (this.localUserId) {
        this.userSessionManager.createSession(
          this.localUserId,
          this.networkOptions.username || `User_${this.localUserId.substring(5)}`,
          this.networkOptions.defaultUserRole,
          true
        );
      }
    }

    // 初始化WebRTC连接管理器
    if (this.networkOptions.enableWebRTC) {
      this.webRTCConnectionManager = new WebRTCConnectionManager({
        iceServers: this.networkOptions.iceServers,
        enableDataChannel: true,
        enableAudio: this.networkOptions.enableAudio,
        enableVideo: this.networkOptions.enableVideo,
        enableScreenShare: this.networkOptions.enableScreenShare,
        useCompression: this.networkOptions.enableCompression,
        maxReconnectAttempts: this.networkOptions.maxReconnectAttempts,
        reconnectInterval: this.networkOptions.reconnectInterval,
        useNetworkQualityMonitor: this.networkOptions.enableNetworkQualityMonitor,
        useBandwidthController: this.networkOptions.enableBandwidthControl,
      });

      // 初始化WebRTC连接管理器
      this.webRTCConnectionManager.initialize(this.localUserId);
    }

    // 初始化媒体流管理器
    if (this.networkOptions.enableMediaStream) {
      this.mediaStreamManager = new MediaStreamManager({
        enableDeviceEnumeration: true,
        enableDeviceChangeDetection: true,
        enableAudioProcessing: true,
        enableVideoProcessing: true,
        enableAutoPlay: true,
        enableAudioLevelMonitoring: true,
      });
    }

    // 初始化实体同步管理器
    if (this.networkOptions.enableEntitySync) {
      this.entitySyncManager = new EntitySyncManager({
        defaultSyncInterval: this.networkOptions.syncInterval,
        useSpatialPartitioning: true,
        useInterpolation: true,
        useExtrapolation: true,
        useCompression: this.networkOptions.enableCompression,
        useDeltaSync: true,
        usePrioritySync: true,
        useAdaptiveSync: true,
      });

      // 初始化实体同步管理器
      if (this.localUserId && this.bandwidthController) {
        this.entitySyncManager.initialize(this.localUserId, this.bandwidthController);
      }
    }

    // 连接网络质量监控器和带宽控制器
    if (this.networkQualityMonitor && this.bandwidthController) {
      // 监听网络质量更新事件
      this.networkQualityMonitor.on('qualityUpdate', (quality) => {
        // 更新带宽控制器的网络质量数据
        this.bandwidthController?.setNetworkQuality(quality);
      });
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (this.connectionState !== NetworkState.CONNECTED) {
      return;
    }

    // 更新网络管理器
    this.networkManager.update(deltaTime);

    // 更新WebRTC连接管理器
    if (this.webRTCConnectionManager) {
      // WebRTC连接管理器没有update方法，但可以在这里添加自定义逻辑
    }

    // 更新实体同步管理器
    if (this.entitySyncManager) {
      // 实体同步管理器通过定时器自动同步，不需要在这里调用
    }

    // 更新网络质量监控器
    if (this.networkQualityMonitor) {
      // 网络质量监控器通过定时器自动采样，不需要在这里调用
    }

    // 更新带宽控制器
    if (this.bandwidthController) {
      // 带宽控制器通过定时器自动调整，不需要在这里调用
    }
  }

  /**
   * 连接到服务器
   * @param serverUrl 服务器URL
   * @param roomId 房间ID
   */
  public connect(serverUrl: string, roomId?: string): void {
    if (this.connectionState === NetworkState.CONNECTING || this.connectionState === NetworkState.CONNECTED) {
      Debug.warn('NetworkSystem', 'Already connected or connecting to server');
      return;
    }

    this.connectionState = NetworkState.CONNECTING;
    this.eventEmitter.emit('connecting');

    this.networkManager.connect(serverUrl, roomId)
      .then(() => {
        this.connectionState = NetworkState.CONNECTED;
        this.reconnectAttempts = 0;
        this.eventEmitter.emit('connected');

        // 启动同步定时器
        this.startSyncTimer();
      })
      .catch((error) => {
        this.connectionState = NetworkState.ERROR;
        this.eventEmitter.emit('error', error);

        // 尝试重连
        this.attemptReconnect();
      });
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    if (this.connectionState === NetworkState.DISCONNECTED || this.connectionState === NetworkState.DISCONNECTING) {
      return;
    }

    this.connectionState = NetworkState.DISCONNECTING;
    this.eventEmitter.emit('disconnecting');

    // 停止同步定时器
    this.stopSyncTimer();

    // 停止重连定时器
    this.stopReconnectTimer();

    this.networkManager.disconnect()
      .then(() => {
        this.connectionState = NetworkState.DISCONNECTED;
        this.eventEmitter.emit('disconnected');
      })
      .catch((error) => {
        this.connectionState = NetworkState.ERROR;
        this.eventEmitter.emit('error', error);
      });
  }

  /**
   * 获取网络连接状态
   * @returns 网络连接状态
   */
  public getConnectionState(): NetworkState {
    return this.connectionState;
  }

  /**
   * 获取本地用户ID
   * @returns 本地用户ID
   */
  public getLocalUserId(): string | null {
    return this.localUserId;
  }

  /**
   * 设置本地用户ID
   * @param userId 用户ID
   */
  public setLocalUserId(userId: string): void {
    this.localUserId = userId;
  }

  /**
   * 获取远程用户
   * @param userId 用户ID
   * @returns 用户实体
   */
  public getRemoteUser(userId: string): Entity | undefined {
    return this.remoteUsers.get(userId);
  }

  /**
   * 获取所有远程用户
   * @returns 用户实体映射表
   */
  public getRemoteUsers(): Map<string, Entity> {
    return this.remoteUsers;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public off(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 发送消息到所有用户
   * @param type 消息类型
   * @param data 消息数据
   */
  public async sendToAll(type: string, data: any): Promise<void> {
    let messageData = data;

    // 如果启用了数据压缩，则压缩数据
    if (this.networkOptions.enableCompression && this.dataCompressor) {
      try {
        const compressedResult = await this.dataCompressor.compress(data);
        messageData = compressedResult.data;
      } catch (error) {
        Debug.warn('NetworkSystem', 'Failed to compress data, sending uncompressed', error);
      }
    }

    this.networkManager.sendToAll(type, messageData);

    // 如果启用了WebRTC，则通过WebRTC发送
    if (this.networkOptions.enableWebRTC && this.webRTCConnectionManager) {
      const message = { type, data: messageData };
      this.webRTCConnectionManager.broadcastMessage(message);
    }
  }

  /**
   * 发送消息到特定用户
   * @param userId 用户ID
   * @param type 消息类型
   * @param data 消息数据
   */
  public async sendToUser(userId: string, type: string, data: any): Promise<void> {
    let messageData = data;

    // 如果启用了数据压缩，则压缩数据
    if (this.networkOptions.enableCompression && this.dataCompressor) {
      try {
        const compressedResult = await this.dataCompressor.compress(data);
        messageData = compressedResult.data;
      } catch (error) {
        Debug.warn('NetworkSystem', 'Failed to compress data, sending uncompressed', error);
      }
    }

    this.networkManager.sendToUser(userId, type, messageData);

    // 如果启用了WebRTC，则通过WebRTC发送
    if (this.networkOptions.enableWebRTC && this.webRTCConnectionManager) {
      const message = { type, data: messageData };
      this.webRTCConnectionManager.sendMessage(userId, message);
    }
  }

  /**
   * 创建WebRTC连接
   * @param userId 远程用户ID
   * @returns WebRTC连接
   */
  public createWebRTCConnection(userId: string): any {
    if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return null;
    }

    return this.webRTCConnectionManager.createConnection(userId);
  }

  /**
   * 处理WebRTC提议
   * @param userId 远程用户ID
   * @param offer 提议
   */
  public handleWebRTCOffer(userId: string, offer: RTCSessionDescriptionInit): void {
    if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return;
    }

    this.webRTCConnectionManager.handleOffer(userId, offer);
  }

  /**
   * 处理WebRTC应答
   * @param userId 远程用户ID
   * @param answer 应答
   */
  public handleWebRTCAnswer(userId: string, answer: RTCSessionDescriptionInit): void {
    if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return;
    }

    this.webRTCConnectionManager.handleAnswer(userId, answer);
  }

  /**
   * 处理WebRTC ICE候选
   * @param userId 远程用户ID
   * @param candidate ICE候选
   */
  public handleWebRTCIceCandidate(userId: string, candidate: RTCIceCandidateInit): void {
    if (!this.options.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return;
    }

    this.webRTCConnectionManager.handleIceCandidate(userId, candidate);
  }

  /**
   * 获取本地媒体流
   * @param type 媒体流类型
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  public async getLocalMediaStream(type: MediaStreamType, config: any = {}): Promise<any> {
    if (!this.options.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return null;
    }

    return this.mediaStreamManager.getLocalStream(type, config);
  }

  /**
   * 停止本地媒体流
   * @param streamId 流ID
   * @returns 是否成功停止
   */
  public stopLocalMediaStream(streamId: string): boolean {
    if (!this.options.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return false;
    }

    return this.mediaStreamManager.stopLocalStream(streamId);
  }

  /**
   * 添加远程媒体流
   * @param stream 媒体流
   * @param userId 用户ID
   * @param type 媒体流类型
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  public addRemoteMediaStream(stream: MediaStream, userId: string, type: MediaStreamType, config: any = {}): any {
    if (!this.options.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return null;
    }

    return this.mediaStreamManager.addRemoteStream(stream, userId, type, config);
  }

  /**
   * 移除远程媒体流
   * @param streamId 流ID
   * @returns 是否成功移除
   */
  public removeRemoteMediaStream(streamId: string): boolean {
    if (!this.options.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return false;
    }

    return this.mediaStreamManager.removeRemoteStream(streamId);
  }

  /**
   * 获取网络质量
   * @returns 网络质量数据
   */
  public getNetworkQuality(): any {
    if (!this.options.enableNetworkQualityMonitor || !this.networkQualityMonitor) {
      Debug.warn('NetworkSystem', 'Network quality monitor is not enabled');
      return null;
    }

    return this.networkQualityMonitor.getCurrentQuality();
  }

  /**
   * 获取带宽使用情况
   * @returns 带宽使用数据
   */
  public getBandwidthUsage(): any {
    if (!this.options.enableBandwidthControl || !this.bandwidthController) {
      Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
      return null;
    }

    return this.bandwidthController.getBandwidthUsage();
  }

  /**
   * 设置带宽控制策略
   * @param strategy 带宽控制策略
   */
  public setBandwidthControlStrategy(strategy: BandwidthControlStrategy): void {
    if (!this.options.enableBandwidthControl || !this.bandwidthController) {
      Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
      return;
    }

    this.bandwidthController.setStrategy(strategy);
  }

  /**
   * 设置最大上行带宽
   * @param bandwidth 带宽（字节/秒）
   */
  public setMaxUploadBandwidth(bandwidth: number): void {
    if (!this.options.enableBandwidthControl || !this.bandwidthController) {
      Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
      return;
    }

    this.bandwidthController.setMaxUploadBandwidth(bandwidth);
  }

  /**
   * 设置最大下行带宽
   * @param bandwidth 带宽（字节/秒）
   */
  public setMaxDownloadBandwidth(bandwidth: number): void {
    if (!this.options.enableBandwidthControl || !this.bandwidthController) {
      Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
      return;
    }

    this.bandwidthController.setMaxDownloadBandwidth(bandwidth);
  }

  /**
   * 添加网络实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public addNetworkEntity(entityId: string, entity: Entity): void {
    if (!this.options.enableEntitySync || !this.entitySyncManager) {
      Debug.warn('NetworkSystem', 'Entity sync is not enabled');
      return;
    }

    this.entitySyncManager.addEntity(entityId, entity);
    this.networkEntities.set(entityId, entity);
  }

  /**
   * 移除网络实体
   * @param entityId 实体ID
   */
  public removeNetworkEntity(entityId: string): void {
    if (!this.options.enableEntitySync || !this.entitySyncManager) {
      Debug.warn('NetworkSystem', 'Entity sync is not enabled');
      return;
    }

    this.entitySyncManager.removeEntity(entityId);
    this.networkEntities.delete(entityId);
  }

  /**
   * 更新网络实体
   * @param entityId 实体ID
   * @param entity 实体
   */
  public updateNetworkEntity(entityId: string, entity: Entity): void {
    if (!this.options.enableEntitySync || !this.entitySyncManager) {
      Debug.warn('NetworkSystem', 'Entity sync is not enabled');
      return;
    }

    this.entitySyncManager.updateEntity(entityId, entity);
  }

  /**
   * 标记实体需要同步
   * @param entityId 实体ID
   */
  public markEntityForSync(entityId: string): void {
    if (!this.options.enableEntitySync || !this.entitySyncManager) {
      Debug.warn('NetworkSystem', 'Entity sync is not enabled');
      return;
    }

    this.entitySyncManager.markEntityForSync(entityId);
  }

  /**
   * 设置实体同步优先级
   * @param entityId 实体ID
   * @param priority 优先级
   */
  public setEntitySyncPriority(entityId: string, priority: number): void {
    if (!this.options.enableEntitySync || !this.entitySyncManager) {
      Debug.warn('NetworkSystem', 'Entity sync is not enabled');
      return;
    }

    this.entitySyncManager.setEntitySyncPriority(entityId, priority);
  }

  /**
   * 设置实体同步间隔
   * @param entityId 实体ID
   * @param interval 同步间隔（毫秒）
   */
  public setEntitySyncInterval(entityId: string, interval: number): void {
    if (!this.options.enableEntitySync || !this.entitySyncManager) {
      Debug.warn('NetworkSystem', 'Entity sync is not enabled');
      return;
    }

    this.entitySyncManager.setEntitySyncInterval(entityId, interval);
  }

  /**
   * 创建用户会话
   * @param userId 用户ID
   * @param username 用户名
   * @param role 角色
   * @param isAuthenticated 是否已验证
   * @returns 用户会话
   */
  public createUserSession(userId: string, username: string, role: UserRole = UserRole.USER, isAuthenticated: boolean = true): any {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return null;
    }

    return this.userSessionManager.createSession(userId, username, role, isAuthenticated);
  }

  /**
   * 获取用户会话
   * @param userId 用户ID
   * @returns 用户会话
   */
  public getUserSession(userId: string): any {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return null;
    }

    return this.userSessionManager.getSession(userId);
  }

  /**
   * 更新用户会话
   * @param userId 用户ID
   * @param updates 更新数据
   * @returns 更新后的会话
   */
  public updateUserSession(userId: string, updates: any): any {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return null;
    }

    return this.userSessionManager.updateSession(userId, updates);
  }

  /**
   * 移除用户会话
   * @param userId 用户ID
   * @returns 是否成功移除
   */
  public removeUserSession(userId: string): boolean {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return false;
    }

    return this.userSessionManager.removeSession(userId);
  }

  /**
   * 检查用户是否有权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否有权限
   */
  public hasPermission(userId: string, permission: UserPermission): boolean {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return false;
    }

    return this.userSessionManager.hasPermission(userId, permission);
  }

  /**
   * 授予用户权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否成功
   */
  public grantPermission(userId: string, permission: UserPermission): boolean {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return false;
    }

    return this.userSessionManager.grantPermission(userId, permission);
  }

  /**
   * 撤销用户权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否成功
   */
  public revokePermission(userId: string, permission: UserPermission): boolean {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return false;
    }

    return this.userSessionManager.revokePermission(userId, permission);
  }

  /**
   * 设置用户角色
   * @param userId 用户ID
   * @param role 角色
   * @returns 是否成功
   */
  public setUserRole(userId: string, role: UserRole): boolean {
    if (!this.options.enableUserSessionManagement || !this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return false;
    }

    return this.userSessionManager.setUserRole(userId, role);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 断开连接
    this.disconnect();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    // 销毁网络管理器
    (this.networkManager as any).dispose();

    // 销毁WebRTC连接管理器
    if (this.webRTCConnectionManager) {
      (this.webRTCConnectionManager as any).dispose();
      this.webRTCConnectionManager = null;
    }

    // 销毁媒体流管理器
    if (this.mediaStreamManager) {
      (this.mediaStreamManager as any).dispose();
      this.mediaStreamManager = null;
    }

    // 销毁实体同步管理器
    if (this.entitySyncManager) {
      (this.entitySyncManager as any).dispose();
      this.entitySyncManager = null;
    }

    // 销毁用户会话管理器
    if (this.userSessionManager) {
      (this.userSessionManager as any).dispose();
      this.userSessionManager = null;
    }

    // 销毁网络事件分发器
    if (this.networkEventDispatcher) {
      (this.networkEventDispatcher as any).dispose();
      this.networkEventDispatcher = null;
    }

    // 销毁网络事件缓冲器
    if (this.networkEventBuffer) {
      (this.networkEventBuffer as any).dispose();
      this.networkEventBuffer = null;
    }

    // 销毁带宽控制器
    if (this.bandwidthController) {
      (this.bandwidthController as any).dispose();
      this.bandwidthController = null;
    }

    // 销毁网络质量监控器
    if (this.networkQualityMonitor) {
      (this.networkQualityMonitor as any).dispose();
      this.networkQualityMonitor = null;
    }

    // 清空网络实体和用户
    this.networkEntities.clear();
    this.remoteUsers.clear();

    super.dispose();
  }

  /**
   * 设置网络事件监听器
   */
  private setupEventListeners(): void {
    // 监听用户加入事件
    this.networkManager.on('userJoined', (userId: string, username: string) => {
      this.handleUserJoined(userId, username);
    });

    // 监听用户离开事件
    this.networkManager.on('userLeft', (userId: string) => {
      this.handleUserLeft(userId);
    });

    // 监听实体创建事件
    this.networkManager.on('entityCreated', (entityId: string, data: any) => {
      this.handleEntityCreated(entityId, data);
    });

    // 监听实体更新事件
    this.networkManager.on('entityUpdated', (entityId: string, data: any) => {
      this.handleEntityUpdated(entityId, data);
    });

    // 监听实体删除事件
    this.networkManager.on('entityDeleted', (entityId: string) => {
      this.handleEntityDeleted(entityId);
    });
  }

  /**
   * 处理用户加入事件
   * @param userId 用户ID
   * @param username 用户名
   */
  private handleUserJoined(userId: string, username: string): void {
    Debug.log('NetworkSystem', `User joined: ${username} (${userId})`);

    // 创建用户实体
    const userEntity = this.world?.createEntity(`User_${username}`) || this.engine?.getWorld()?.createEntity(`User_${username}`);

    if (userEntity) {
      // 添加网络用户组件
      const networkUserComponent = new NetworkUserComponent(userEntity, {
        userId,
        username,
        isLocal: false,
      });
      userEntity.addComponent(networkUserComponent);

      // 添加到远程用户映射表
      this.remoteUsers.set(userId, userEntity);

      // 触发用户加入事件
      this.eventEmitter.emit('userJoined', userId, username, userEntity);
    }
  }

  /**
   * 处理用户离开事件
   * @param userId 用户ID
   */
  private handleUserLeft(userId: string): void {
    Debug.log('NetworkSystem', `User left: ${userId}`);

    // 获取用户实体
    const userEntity = this.remoteUsers.get(userId);

    if (userEntity) {
      // 销毁用户实体
      this.world?.removeEntity(userEntity) || this.engine?.getWorld()?.removeEntity(userEntity);

      // 从远程用户映射表中移除
      this.remoteUsers.delete(userId);

      // 触发用户离开事件
      this.eventEmitter.emit('userLeft', userId);
    }
  }

  /**
   * 处理实体创建事件
   * @param entityId 实体ID
   * @param data 实体数据
   */
  private handleEntityCreated(entityId: string, data: any): void {
    // 创建实体
    const entity = this.world?.createEntity(`Entity_${entityId}`) || this.engine?.getWorld()?.createEntity(`Entity_${entityId}`);

    if (entity) {
      // 添加网络实体组件
      const networkEntityComponent = new NetworkEntityComponent({
        entityId,
        ownerId: data.ownerId,
      });
      entity.addComponent(networkEntityComponent);

      // 根据数据添加其他组件
      this.applyEntityData(entity, data);

      // 添加到网络实体映射表
      this.networkEntities.set(entityId, entity);

      // 触发实体创建事件
      this.eventEmitter.emit('entityCreated', entityId, entity);
    }
  }

  /**
   * 处理实体更新事件
   * @param entityId 实体ID
   * @param data 实体数据
   */
  private handleEntityUpdated(entityId: string, data: any): void {
    // 获取实体
    const entity = this.networkEntities.get(entityId);

    if (entity) {
      // 应用实体数据
      this.applyEntityData(entity, data);

      // 触发实体更新事件
      this.eventEmitter.emit('entityUpdated', entityId, entity);
    }
  }

  /**
   * 处理实体删除事件
   * @param entityId 实体ID
   */
  private handleEntityDeleted(entityId: string): void {
    // 获取实体
    const entity = this.networkEntities.get(entityId);

    if (entity) {
      // 销毁实体
      this.world?.removeEntity(entity) || this.engine?.getWorld()?.removeEntity(entity);

      // 从网络实体映射表中移除
      this.networkEntities.delete(entityId);

      // 触发实体删除事件
      this.eventEmitter.emit('entityDeleted', entityId);
    }
  }

  /**
   * 应用实体数据
   * @param entity 实体
   * @param data 实体数据
   */
  private applyEntityData(entity: Entity, data: any): void {
    // 应用变换数据
    if (data.transform) {
      let transform = entity.getComponent('Transform') as any;

      // 如果实体没有Transform组件，则创建一个
      if (!transform) {
        // 导入Transform组件
        const { Transform } = require('../scene/Transform');
        transform = new Transform();
        entity.addComponent(transform);
      }

      // 修复：直接使用data.transform的属性，而不是调用getPosition()方法
      if (data.transform.position) {
        transform.setPosition(
          data.transform.position.x,
          data.transform.position.y,
          data.transform.position.z
        );
      }

      if (data.transform.rotation) {
        transform.setRotationQuaternion(
          data.transform.rotation.x,
          data.transform.rotation.y,
          data.transform.rotation.z,
          data.transform.rotation.w
        );
      }

      if (data.transform.scale) {
        transform.setScale(
          data.transform.scale.x,
          data.transform.scale.y,
          data.transform.scale.z
        );
      }
    }

    // 应用网络实体组件数据
    if (data.networkEntity) {
      let networkEntityComponent = entity.getComponent(NetworkEntityComponent.type) as NetworkEntityComponent;
      if (!networkEntityComponent) {
        networkEntityComponent = new NetworkEntityComponent({
          entityId: data.networkEntity.entityId,
          ownerId: data.networkEntity.ownerId,
        });
        entity.addComponent(networkEntityComponent);
      }

      // 更新网络实体组件数据
      if (data.networkEntity.syncPriority !== undefined) {
        networkEntityComponent.syncPriority = data.networkEntity.syncPriority;
      }
    }

    // 应用网络变换组件数据
    if (data.networkTransform) {
      let networkTransformComponent = entity.getComponent(NetworkTransformComponent.type) as NetworkTransformComponent;
      if (!networkTransformComponent) {
        networkTransformComponent = new NetworkTransformComponent({
          syncPosition: data.networkTransform.syncPosition !== false,
          syncRotation: data.networkTransform.syncRotation !== false,
          syncScale: data.networkTransform.syncScale !== false,
        });
        entity.addComponent(networkTransformComponent);
      }
    }

    // 应用用户组件数据
    if (data.user) {
      let networkUserComponent = entity.getComponent(NetworkUserComponent.type) as NetworkUserComponent;
      if (!networkUserComponent) {
        networkUserComponent = new NetworkUserComponent(entity, {
          userId: data.user.userId,
          username: data.user.username,
          isLocal: data.user.isLocal || false,
        });
        entity.addComponent(networkUserComponent);
      }
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts!) {
      Debug.error('NetworkSystem', 'Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;

    Debug.log('NetworkSystem', `Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);

    // 设置重连定时器
    this.reconnectTimerId = window.setTimeout(() => {
      this.connect(this.options.serverUrl!, this.options.roomId);
    }, this.options.reconnectInterval);
  }

  /**
   * 停止重连定时器
   */
  private stopReconnectTimer(): void {
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimerId !== null) {
      return;
    }

    this.syncTimerId = window.setInterval(() => {
      this.syncNetworkEntities();
    }, this.options.syncInterval);
  }

  /**
   * 停止同步定时器
   */
  private stopSyncTimer(): void {
    if (this.syncTimerId !== null) {
      clearInterval(this.syncTimerId);
      this.syncTimerId = null;
    }
  }

  /**
   * 同步网络实体
   */
  private syncNetworkEntities(): void {
    // 同步本地拥有的网络实体
    for (const [entityId, entity] of this.networkEntities.entries()) {
      const networkEntity = entity.getComponent(NetworkEntityComponent.type) as NetworkEntityComponent;

      // 只同步本地拥有的实体
      if (networkEntity && networkEntity.ownerId === this.localUserId) {
        const transform = entity.getComponent('Transform') as any;

        if (transform) {
          // 获取变换数据
          const position = transform.getPosition();
          const rotation = transform.getRotationQuaternion();
          const scale = transform.getScale();

          // 发送变换数据
          this.networkManager.sendEntityUpdate(entityId, {
            transform: {
              position: {
                x: position.x,
                y: position.y,
                z: position.z,
              },
              rotation: {
                x: rotation.x,
                y: rotation.y,
                z: rotation.z,
                w: rotation.w,
              },
              scale: {
                x: scale.x,
                y: scale.y,
                z: scale.z,
              },
            },
          });
        }
      }
    }
  }

  /**
   * 发送请求到微服务
   * @param serviceName 服务名称
   * @param endpoint 端点
   * @param options 请求选项
   * @returns 响应数据
   */
  public async requestService<T = any>(serviceName: string, endpoint: string, options: any = {}): Promise<T> {
    if (!this.options.enableMicroserviceClient || !this.microserviceClient) {
      throw new Error('Microservice client is not enabled');
    }

    return this.microserviceClient.request<T>(serviceName, endpoint, options);
  }

  /**
   * 注册服务实例
   * @param serviceName 服务名称
   * @param host 主机名
   * @param port 端口
   * @param secure 是否安全连接
   * @param metadata 元数据
   * @returns 服务实例
   */
  public async registerService(
    serviceName: string,
    host: string,
    port: number,
    secure: boolean = false,
    metadata: Record<string, any> = {}
  ): Promise<ServiceInstance> {
    if (!this.options.enableServiceDiscovery || !this.serviceDiscoveryClient) {
      throw new Error('Service discovery is not enabled');
    }

    return this.serviceDiscoveryClient.registerService(serviceName, host, port, secure, metadata);
  }

  /**
   * 发现服务实例
   * @param serviceName 服务名称
   * @returns 服务实例列表
   */
  public async discoverService(serviceName: string): Promise<ServiceInstance[]> {
    if (!this.options.enableServiceDiscovery || !this.serviceDiscoveryClient) {
      throw new Error('Service discovery is not enabled');
    }

    return this.serviceDiscoveryClient.discoverService(serviceName);
  }

  /**
   * 设置认证令牌
   * @param token 认证令牌
   */
  public setAuthToken(token: string): void {
    if (this.microserviceClient) {
      this.microserviceClient.setAuthToken(token);
    }
  }

  /**
   * 分发网络事件
   * @param type 事件类型
   * @param data 事件数据
   * @param senderId 发送者ID
   * @param receiverId 接收者ID
   * @param priority 优先级
   */
  public dispatchNetworkEvent(
    type: string,
    data?: any,
    senderId?: string,
    receiverId?: string,
    priority?: EventPriority
  ): void {
    if (this.networkEventDispatcher) {
      const event = this.networkEventDispatcher.createEvent(type, data, senderId, receiverId, priority);
      this.networkEventDispatcher.dispatchEvent(event);
    }
  }

  /**
   * 订阅网络事件
   * @param eventType 事件类型
   * @param handler 处理器
   * @param options 选项
   * @returns 订阅ID
   */
  public subscribeNetworkEvent(
    eventType: string,
    handler: (event: any) => void,
    options: {
      filter?: (event: any) => boolean;
      priority?: EventPriority;
      once?: boolean;
    } = {}
  ): string | null {
    if (this.networkEventDispatcher) {
      return this.networkEventDispatcher.subscribe(eventType, handler, options);
    }
    return null;
  }

  /**
   * 取消订阅网络事件
   * @param eventType 事件类型
   * @param handler 处理器
   * @returns 是否成功取消
   */
  public unsubscribeNetworkEvent(eventType: string, handler: (event: any) => void): boolean {
    if (this.networkEventDispatcher) {
      return this.networkEventDispatcher.unsubscribe(eventType, handler);
    }
    return false;
  }

  /**
   * 获取网络事件缓冲区大小
   * @returns 缓冲区大小
   */
  public getNetworkEventBufferSize(): number {
    if (this.networkEventBuffer) {
      return this.networkEventBuffer.getBufferSize();
    }
    return 0;
  }

  /**
   * 清空网络事件缓冲区
   */
  public clearNetworkEventBuffer(): void {
    if (this.networkEventBuffer) {
      this.networkEventBuffer.clearBuffer();
    }
  }

  /**
   * 设置事件类型优先级
   * @param eventType 事件类型
   * @param priority 优先级
   */
  public setEventTypePriority(eventType: string, priority: EventPriority): void {
    if (this.networkEventBuffer) {
      this.networkEventBuffer.setEventTypePriority(eventType, priority);
    }
  }
}
